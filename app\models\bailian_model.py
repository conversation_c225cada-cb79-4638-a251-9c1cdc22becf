import logging
from dashscope import Application
from app.config import Config
import json

logger = logging.getLogger(__name__)

class BailianModel:
    """阿里云百炼模型封装类"""
    
    def __init__(self):
        """初始化百炼模型"""
        self.config = Config.BAILIAN_CONFIG
        self.api_key = self.config['api_key']
        self.app_id = self.config['app_id']
        self.workspace = self.config['workspace']
          
    def chat_completion_stream(self, messages, image_list=None):
        """
        百炼聊天完成接口（流式输出）
        
        Args:
            messages: 对话消息列表，格式：[{"role": "user", "content": "你好"}]
            image_list: 图片链接列表，可选参数
            
        Yields:
            str: 流式输出的响应数据
        """
        try:
            logger.info(f"开始调用百炼API，消息数量: {len(messages)}")
            
            # 构建请求参数
            params = {
                'api_key': self.api_key,
                'app_id': self.app_id,
                'workspace': self.workspace,
                'messages': messages,
                'stream': True,
                'incremental_output': True
            }
            
            # 如果有图片列表，添加到参数中
            if image_list:
                params['image_list'] = image_list
                logger.info(f"包含图片数量: {len(image_list)}")
            
            # 调用百炼API
            response = Application.call(**params)

            # 处理流式响应
            for chunk in response:
                # 检查是否有错误
                if hasattr(chunk, 'code') and chunk.code:
                    error_data = {
                        "request_id": getattr(chunk, 'request_id', ''),
                        "code": chunk.code,
                        "message": getattr(chunk, 'message', '请求失败')
                    }
                    logger.error(f"百炼API调用失败: {error_data}")
                    yield f"{json.dumps(error_data, ensure_ascii=False)}\n\n"
                    return

                if hasattr(chunk, 'output') and hasattr(chunk.output, 'text'):
                    # 构建流式响应数据
                    chunk_data = {
                        "request_id": getattr(chunk, 'request_id', ''),
                        "output": {
                            "text": chunk.output.text,
                            "finish_reason": getattr(chunk.output, 'finish_reason', None),
                            "session_id": getattr(chunk.output, 'session_id', '')
                        }
                    }

                    yield f"{json.dumps(chunk_data, ensure_ascii=False)}\n\n"
                        
        except Exception as e:
            logger.error(f"百炼API调用异常: {str(e)}")
            error_data = {
                "code": "InternalError",
                "message": f"服务器内部错误: {str(e)}"
            }
            yield f"{json.dumps(error_data, ensure_ascii=False)}\n\n"
