from flask_restx import Namespace, Resource, fields
from flask import request, Response, stream_with_context, jsonify
from app.models.bailian_model import BailianModel
import logging
import json

# 创建命名空间
bailian_ns = Namespace('阿里云百炼', description='阿里云百炼智能体接口')

# 定义请求和响应模型
message_model = bailian_ns.model('Message', {
    'role': fields.String(required=True, description='消息角色(system/user/assistant)'),
    'content': fields.String(required=True, description='消息内容')
})

bailian_request = bailian_ns.model('BailianRequest', {
    'messages': fields.List(fields.Nested(message_model), required=True, description='对话消息列表'),
    'image_list': fields.List(fields.String, description='图片链接列表，可选参数')
})

bailian_response = bailian_ns.model('BailianResponse', {
    'request_id': fields.String(description='请求ID'),
    'output': fields.Raw(description='输出结果'),
    'usage': fields.Raw(description='使用统计'),
    'code': fields.String(description='错误码'),
    'message': fields.String(description='错误信息')
})

logger = logging.getLogger(__name__)


@bailian_ns.route('/sse')
class BailianChatAPI(Resource):
    @bailian_ns.expect(bailian_request)
    @bailian_ns.response(200, 'Success', bailian_response)
    @bailian_ns.response(400, 'Bad Request')
    @bailian_ns.response(500, 'Internal Server Error')
    def post(self):
        """
        阿里云百炼聊天接口（流式输出）
        
        功能说明：
        - 接收messages和image_list参数
        - 调用阿里云百炼API
        - 返回流式响应数据
        - 只支持流式输出，不支持非流式
        
        请求参数：
        - messages: 对话消息列表，必传，格式：[{"role": "user", "content": "你好"}]
        - image_list: 图片链接列表，可选，格式：["http://example.com/image1.jpg"]
        
        响应格式：
        - 流式输出，每个数据块以 "data: " 开头
        - 数据格式为JSON，包含request_id、output、usage等字段
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "无效的请求数据"}), 400
            
            messages = data.get('messages', [])
            image_list = data.get('image_list')
            
            # 验证必要参数
            if not messages:
                return jsonify({"error": "消息列表不能为空"}), 400
            
            # 验证messages格式
            if not isinstance(messages, list):
                return jsonify({"error": "messages必须是数组格式"}), 400
            
            # 验证image_list格式（如果提供）
            if image_list is not None and not isinstance(image_list, list):
                return jsonify({"error": "image_list必须是数组格式"}), 400
            
            if image_list:
                logger.info(f"包含图片数量: {len(image_list)}")
            
            # 创建百炼模型实例
            bailian = BailianModel()
            
            # 定义流式响应生成器
            def generate():
                try:
                    for chunk in bailian.chat_completion_stream(messages, image_list):
                        yield chunk
                except Exception as e:
                    logger.error(f"流式响应生成错误: {str(e)}")
                    error_data = {
                        "code": "StreamError",
                        "message": f"流式响应错误: {str(e)}"
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            
            # 返回流式响应
            return Response(
                stream_with_context(generate()),
                mimetype='text/plain',
                headers={
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Content-Type': 'text/plain; charset=utf-8'
                }
            )
            
        except Exception as e:
            logger.error(f"百炼聊天接口错误: {str(e)}")
            return jsonify({"error": f"服务器内部错误: {str(e)}"}), 500
